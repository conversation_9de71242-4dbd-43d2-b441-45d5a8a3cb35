#include "pch.h"

// Merge sections into each other
//#pragma warning(disable : 4530)
//#pragma comment(linker, "/merge:.CRT=.rdata")
//#pragma comment(linker, "/merge:_RDATA=.rdata")
//#pragma comment(linker, "/merge:.pdata=.rdata")
//#pragma comment(linker, "/merge:.rdata=.text")

#include "Hooks/HookManager/HookManager.h"
#include "ModuleSystem/ModuleBase/Settings/Config/Config.h"
#include "ModuleSystem/ModuleManager/ModuleManager.h"
#include "Utils/MinHook/MinHook.h"
#include "Utils/Memory/SignatureScanner.h"

namespace
{
    // Thread-safe shutdown flag
    volatile bool g_shouldShutdown = false;
    HANDLE g_mainThread = nullptr;

    // Console management - only for DEBUG builds
    bool InitializeConsole()
    {
#ifdef DEBUG
        if (!AllocConsole())
            return false;
        
        if (freopen_s(&Global::File, XOR("CONOUT$"), XOR("w"), stdout) != 0)
        {
            FreeConsole();
            return false;
        }
        
        printf(XOR("Horizon Client Initialized\n"));
        return true;
#else
        // No console in RELEASE builds
        return true;
#endif
    }

    void CleanupConsole()
    {
#ifdef DEBUG
        if (Global::File)
        {
            fclose(Global::File);
            Global::File = nullptr;
        }
        FreeConsole();
#endif
    }

    // Core initialization sequence
    bool InitializeCore()
    {
        try
        {
            // Initialize MinHook first
            MH_STATUS status = MH_Initialize();
            if (status != MH_OK && status != MH_ERROR_ALREADY_INITIALIZED)
            {
#ifdef DEBUG
                printf(XOR("Failed to initialize MinHook: %s\n"), MH_StatusToString(status));
#endif
                return false;
            }

            // Initialize module manager
            Base::ModuleManager::Initialize();
            
            // Initialize SDK
            CG::BasicTypes::InitSdk();
            
            // Generate configuration
            Base::GenerateConfig();
            
            // Initialize FName cache
            Base::FNameCache::Initialize();
            
            // Unlock DLCs
            SignatureScanner::UnlockDLCs();
            
            // Initialize hooks
            InitializeHooks();
            
            return true;
        }
        catch (...)
        {
#ifdef DEBUG
            printf(XOR("Exception during initialization\n"));
#endif
            return false;
        }
    }

    // Cleanup sequence
    void CleanupCore()
    {
        try
        {
            // Disable hooks first
            DisableHooks();
            
            // Give time for hooks to be properly removed
            Sleep(200);
            
            // Uninitialize MinHook
            MH_Uninitialize();
            
            // Cleanup console
            CleanupConsole();
        }
        catch (...)
        {
            // Ignore cleanup errors during shutdown
        }
    }

    // Main thread function for DEBUG builds
    DWORD WINAPI MainThread(LPVOID param)
    {
        // Initialize console if needed
        if (!InitializeConsole())
        {
#ifdef DEBUG
            // In DEBUG, we can still continue without console
#else
            return 1;
#endif
        }

        // Initialize core systems
        if (!InitializeCore())
        {
            CleanupConsole();
            return 1;
        }

#ifdef DEBUG
        // DEBUG build: Monitor for uninjection key (VK_END)
        while (!g_shouldShutdown)
        {
            if (GetAsyncKeyState(VK_END) & 0x8000)
            {
                g_shouldShutdown = true;
                break;
            }
            Sleep(50); // Reduce CPU usage
        }
        
        // Cleanup and exit
        CleanupCore();
        FreeLibraryAndExitThread(Global::HModule, 0);
#endif

        return 0;
    }
}

BOOL APIENTRY DllMain(HMODULE hModule,
    DWORD ulReasonForCall,
    LPVOID lpReserved
)
{
    if (ulReasonForCall == DLL_PROCESS_ATTACH)
    {
        // Store module handle for later use
        Global::HModule = hModule;

        // Disable thread library calls for performance
        DisableThreadLibraryCalls(hModule);

#ifdef DEBUG
        // Create main thread for DEBUG builds
        g_mainThread = CreateThread(nullptr, 0, MainThread, nullptr, 0, nullptr);
        if (!g_mainThread)
        {
            return FALSE;
        }
#endif

#ifdef RELEASE
        // RELEASE build: Direct initialization with delay (no console)
        Sleep(20000); // Anti-detection delay
        
        // Initialize core systems (no console in RELEASE)
        if (!InitializeCore())
        {
            return FALSE;
        }
#endif
    }
    else if (ulReasonForCall == DLL_PROCESS_DETACH)
    {
        // Signal shutdown
        g_shouldShutdown = true;
        
        // Wait for main thread to finish (with timeout)
        if (g_mainThread)
        {
            WaitForSingleObject(g_mainThread, 5000);
            CloseHandle(g_mainThread);
            g_mainThread = nullptr;
        }
        
        // Final cleanup
        CleanupCore();
    }

    return TRUE;
}
