// pch.h: This is a precompiled header file.
// Files listed below are compiled only once, improving build performance for future builds.
// This also affects IntelliSense performance, including code completion and many code browsing features.
// However, files listed here are ALL re-compiled if any one of them is updated between builds.
// Do not add files here that you will be updating frequently as this negates the performance advantage.

#ifndef PCH_H
#define PCH_H

// add headers that you want to pre-compile here
#define DEBUG // DEBUG/RELEASE
//#define THREE_MINUTES

#include <iostream>
#include <fstream>
#include <cstdio>
#include <list>
#include <string>
#include <cstdlib> // for _dupenv_s
#include <filesystem> // for std::filesystem::path and std::filesystem::exists

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <set>
#include <locale>
#include <unordered_set>
#include <unordered_map>
#include <sstream>
#include <cstdint>
#include <Windows.h>
#include <Psapi.h>
#include <vector>
#include <chrono>
#include <cassert>
#include <codecvt>
#include <cstdint>
#include <iostream>
#include <locale>
#include <string>

#include "Utils/Crypter/Crypter.h"

#include "SDK/Headers/ShooterGame.h"
#include "SDK/Headers/BasicTypes.h"
#include "SDK/Headers/Engine.h"
#include "SDK/Headers/CoreUObject.h"

#include "Headers/Engine_FURL.h"
#include <Headers/Dino_Character_BP_ADino_Character_BP_C.h>
#include <Headers/ShooterGame_UPrimalInventoryComponent.h>
#include "Headers/Engine_UGameplayStatics.h"
#include "Headers/ShooterGame_UVictoryCore.h"
#include "Headers/CoreUObject_UClass.h"
#include "Headers/ShooterGame_UShooterGameInstance.h"
#include "Headers/Engine_UKismetSystemLibrary.h"
#include "Headers/Engine_UKismetMathLibrary.h"
#include "Headers/Engine_UKismetStringLibrary.h"
#include "Headers/Foliage_AInstancedFoliageActor.h"
#include "Headers/ShooterGame_UShooterLocalPlayer.h"
#include "Headers/ShooterGame_AShooterPlayerController.h"
#include "Headers/ShooterGame_APrimalStructureItemContainer_SupplyCrate.h"
#include "Headers/ShooterGame_AShooterCharacter.h"
#include "Headers/ShooterGame_UShooterGameViewportClient.h"
#include "Headers/ShooterGame_UShooterCharacterMovement.h"
#include "Headers/Engine_ULevel.h"
#include "Headers/ShooterGame_AShooterPlayerCameraManager.h"
#include "Headers/ShooterGame_AShooterGameState.h"
#include "Headers/ShooterGame_PARAMS.h"

#include "Headers/ShooterGame_APrimalWorldSettings.h"
#include "Headers/ShooterGame_UMinimapData.h"
#include "Headers/Engine_UPrimalWorld.h"
#include "Headers/Engine_UWorld.h"
#include "Headers/Engine_ULevelStreaming.h"
#include "Headers/Engine_ULevelStreamingAlwaysLoaded.h"
#include "Headers/Engine_APlayerStart.h"
#include "Headers/Foliage_ENUMS.h"
#include "Headers/Foliage_PARAMS.h"
#include "Headers/Foliage_UFoliageInstancedStaticMeshComponent.h"

#include "Headers/ShooterGame_UNPCSpawnEntriesContainer.h"
#include "Headers/ShooterGame_ANPCZoneManager.h"
#include "Headers/ShooterGame_ANPCZoneSpawnVolume.h"
#include "Headers/ShooterGame_ANPCZoneVolume.h"
#include "Headers/Engine_UDynamicBlueprintBinding.h"
#include "Headers/Engine_UBlueprintGeneratedClass.h"
#include "Headers/Engine_UWorldPartitionRuntimeSpatialHash.h"
#include "Headers/Engine_UWorldPartition.h"


#include "Headers/Engine_UFont.h"
#include "Headers/Engine_UFontFace.h"

#include "Headers/PhysicsCore_ENUMS.h"
#include <SDK/Headers/PhysicsCore.h>
#include <SDK/Headers/PhysicsCore_UPhysicalMaterial.h>
#include "Headers/ShooterGame_APrimalCharacter.h"
#include "Headers/Engine_UPrimitiveComponent.h"

#include "Headers/Engine_PARAMS.h"
#include "Headers/ShooterGame_AShooterWeapon.h"
#include "Headers/ShooterGame_APrimalWeaponGrenade.h"
#include "Headers/ShooterGame_AShooterHUD.h"
#include "Headers/Engine_UCanvas.h"
#include "Headers/Engine_UConsole.h"
#include <Headers/BP_DedicatedStorage_ABP_DedicatedStorage_C.h>
#include "Headers/ShooterGame_UPrimalCharacterMovement.h"
#include "Headers/ShooterGame_UPrimalCharacterSetting.h"
#include "Headers/ShooterGame_UPrimalCharacterStatusComponent.h"
#include "Headers/ShooterGame_UPrimalHarvestingComponent.h"
#include <Headers/ExplorerChest_Base_AExplorerChest_Base_C.h>

#include "Headers/ShooterGame_APrimalStructureBearTrap.h"
#include "Headers/ShooterGame_APrimalStructureSeating.h"
#include "Headers/ShooterGame_APrimalStructureBed.h"
#include "Headers/ShooterGame_APrimalStructureDoor.h"
#include "Headers/ShooterGame_APrimalStructureElevatorPlatform.h"
#include "Headers/ShooterGame_APrimalStructureElevatorTrack.h"
#include "Headers/ShooterGame_APrimalStructureExplosive.h"

#include "Headers/StorageBox_TekGenerator_PARAMS.h"
#include "Headers/StorageBox_TekGenerator_AStorageBox_TekGenerator_C.h"
#include "Headers/ElectricGenerator_PARAMS.h"
#include "Headers/ElectricGenerator_AElectricGenerator_C.h"
#include "Headers/CryoFridge_PARAMS.h"
#include "Headers/CryoFridge_ACryoFridge_C.h"
#include "Headers/ShooterGame_ADroppedItem.h"
#include "Headers/ShooterGame_ADroppedItemEgg.h"
#include <Headers/DroppedItemGeneric_ADroppedItemGeneric_C.h>
#include "Headers/DeathItemCache_ADeathItemCache_C.h"
#include "Headers/ShooterGame_APrimalStructurePlacer.h"
#include "Headers/Engine_APlayerState.h"
#include "Headers/Engine_UNetConnection.h"
#include "Headers/BasicTypes_FUObjectItem.h"
#include "Headers/DarkPegasus_Character_BP_PARAMS.h"
#include "Headers/DarkPegasus_Character_BP_ADarkPegasus_Character_BP_C.h"
#include <Headers/Buff_TekArmor_Pants_ABuff_TekArmor_Pants_C.h>
#include <Headers/Buff_TekArmor_Shirt_Rework_ABuff_TekArmor_Shirt_Rework_C.h>
#include <Headers/Buff_DinoCompanion_Base_BP_PARAMS.h>
#include <Headers/PrimalItemStructureGeneric_UPrimalItemStructureGeneric_C.h>
#include "Headers/ShooterGame_AShooterPlayerState.h"

#include <Headers/PrimalItemAmmo_AdvancedRifleBullet_UPrimalItemAmmo_AdvancedRifleBullet_C.h>
#include <Headers/PrimalItemResource_Fibers_UPrimalItemResource_Fibers_C.h>
#include <Headers/PrimalItem_WeaponRifle_UPrimalItem_WeaponRifle_C.h>
#include <Headers/CropPlotBaseBP_ACropPlotBaseBP_C.h>

#include "Headers/TrackBinocularsBuff_PARAMS.h"
#include "Headers/TrackBinocularsBuff_ATrackBinocularsBuff_C.h"

#include "Headers/Engine_UKismetGuidLibrary.h"
#include "Headers/Engine_UKismetInputLibrary.h"
#include "Headers/Engine_UKismetInternationalizationLibrary.h"
#include "Headers/Engine_UKismetMaterialLibrary.h"
#include "Headers/Engine_UMaterialInstanceConstant.h"

#include <Headers/StorageBox_TekGenerator_AStorageBox_TekGenerator_C.h>
#include <Headers/StructureTurretBaseBP_BaseHeavy_AStructureTurretBaseBP_BaseHeavy_C.h>
#include <Headers/StructureTurretTek_AStructureTurretTek_C.h>
#include "Headers/Engine_UMaterial.h"
#include "Headers/Engine_UMaterialInstanceEditorOnlyData.h"
#include "Headers/Engine_UMaterialInstanceDynamic.h"
#include "Headers/Engine_UMaterialParameterCollection.h"

#include "Headers/Engine_UStaticMeshComponent.h"
#include "Headers/Engine_UInstancedStaticMeshComponent.h"
#include "Headers/Engine_UMaterialInterface.h"
#include "Headers/Engine_USkeletalMeshComponent.h"

#include "Headers/ShooterGame_AShooterWeapon_FlameThrower.h"
#include "Headers/ShooterGame_AShooterWeapon_Activated.h"
#include "Headers/ShooterGame_AShooterWeapon_ChainSaw.h"
#include "Headers/ShooterGame_AShooterWeapon_Climb.h"
#include "Headers/ShooterGame_AShooterWeapon_Instant.h"
#include "Headers/ShooterGame_AShooterWeapon_InstantCharging.h"
#include "Headers/ShooterGame_AShooterWeapon_InstantPenetrating.h"
#include "Headers/ShooterGame_AShooterWeapon_MeleeLock.h"
#include "Headers/ShooterGame_AShooterWeapon_Placer.h"
#include "Headers/ShooterGame_AShooterWeapon_Whip.h"

#include <Headers/Buff_DinoCompanion_Base_BP_ABuff_DinoCompanion_Base_BP_C.h>

#include "Headers/Global_DEFINES.h"
#include "Headers/BasicTypes.h"
#include "Headers/CoreUObject_UFunction.h"
#include "Headers/AssetRegistry_ENUMS.h"
#include "Headers/AssetRegistry_PARAMS.h"
#include "Headers/AssetRegistry_IAssetRegistry.h"
#include "Headers/AssetRegistry_UAssetRegistryImpl.h"
#include "Headers/AssetRegistry_UAssetRegistryHelpers.h"

#include "Utils/Config/JSON.h"
#include "Utils/Global/Global.h"
#include "Utils/Memory/SignatureScanner.h"
#include "Utils/FNameCache/FNameCache.h"
#include "ModuleSystem/ModuleBase/Module.h"

#endif //PCH_H