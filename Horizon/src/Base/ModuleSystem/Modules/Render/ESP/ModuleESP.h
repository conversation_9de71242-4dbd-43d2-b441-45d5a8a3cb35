#pragma once

#include "pch.h"

namespace Base
{
	class ModuleESP final : public Module
	{
	public:
		ModuleESP(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon) : Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;

		void LoadSettings(nlohmann::json config) override;

		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

		void DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale);

		void DrawTextY(CG::Engine::AHUD* HUD, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue);

		void RenderDino(CG::Engine::UFont* font, CG::ShooterGame::AShooterPlayerController* playerController, CG::Engine::AHUD* hud, CG::ShooterGame::APrimalDinoCharacter* dino);
		void RenderPlayer(CG::Engine::UFont* font, CG::ShooterGame::AShooterPlayerController* playerController, CG::Engine::AHUD* hud, CG::ShooterGame::AShooterCharacter* player);

	private:
		bool m_friendlyPlayer = false;
		bool m_enemyPlayer = false;
		bool m_sleepingPlayer = false;
		bool m_deadPlayer = false;
		bool m_extendedPlayerInfo = false;
		bool m_friendlyDino = false;
		bool m_enemyDino = false;
		bool m_deadDino = false;
		bool m_wildDino = false;
		bool m_alphaDino = false;
		bool m_vault = false;
		bool m_dedicatedStorage = false;
		// bool m_storageBox = false;
		bool m_supplyCrate = false;
		bool m_item = false;
		bool m_itemCache = false;

		bool m_hideHealth = false;
		bool m_hideTribe = false;
		bool m_hideAggression = false;

		bool m_loadoutDummy = false;
		bool m_storageBoxSmall = false;
		bool m_storageBoxLarge = false;
		bool m_turret = false;
		bool m_bed = false;
		bool m_tekGenerator = false;
		bool m_electricGenerator = false;
		bool m_cryoFridge = false;

		bool m_hideEmpty = false;
		bool m_hideItemAmount = false;

		bool m_hideEnemyStructures = false;
		bool m_hideFriendlyStructures = true;
		bool m_hideTurretSettings = false;

		bool m_tekSensor = false;

		bool m_hideStructureHealth = true;

		bool m_showSpawnpoints = false;

		bool m_notes = false;

		bool m_ammoBox = false;

		bool m_unlocked = false;
		bool m_exRender = true;

		bool m_eggs = false;
		bool m_levelFilter = false;
		bool m_hideDinoLevel = false;

		// Resource ESP
		bool m_resourceEsp = false;
		bool m_metal = true;
		bool m_oil = false;
		bool m_crystal = false;
		bool m_obsidian = false;
		bool m_silica = false;
		bool m_silk = false;

		// Dino Stats
		bool m_stamina = false;
		bool m_oxygen = false;
		bool m_food = false;
		bool m_weight = false;
		bool m_meleeDamage = false;
		bool m_movementSpeed = false;
		bool m_torpor = false;

		bool m_dinoNameFilter = false;

		std::string m_textboxDinoNameFilter = "";
		std::string m_textboxDinoNameFilter2 = "";
		std::string m_textboxDinoNameFilter3 = "";
		std::string m_textboxDinoNameFilter4 = "";
		std::string m_textboxDinoNameFilter5 = "";
		std::string m_textboxDinoNameFilter6 = "";
		std::string m_textboxDinoNameFilter7 = "";
		std::string m_textboxDinoNameFilter8 = "";

		CG::CoreUObject::FLinearColor m_structureColor = { 1.f, 1.f, 1.f, 1.f };
		CG::CoreUObject::FLinearColor m_dinoColor = { 1.f, 1.f, 1.f, 1.f };
		CG::CoreUObject::FLinearColor m_enemyDinoColor = { 1.f, 1.f, 1.f, 1.f };
		CG::CoreUObject::FLinearColor m_playerColor = { 1.f, 1.f, 1.f, 1.f };
		CG::CoreUObject::FLinearColor m_enemyPlayerColor = { 1.f, 1.f, 1.f, 1.f };
		CG::CoreUObject::FLinearColor m_healthColor = { 1.f, 1.f, 1.f, 1.f };
		CG::CoreUObject::FLinearColor m_dinoStatsColor = { 1.f, 1.f, 1.f, 1.f };

		int m_espRange = 1000;

		float m_textScale = 5.f;
		int m_levelSlider = 150;
		int m_resourceRenderDistance = 30000;
		int m_resourceRenderReload = 5;
	};

}
